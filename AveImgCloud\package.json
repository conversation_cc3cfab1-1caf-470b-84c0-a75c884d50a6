{"name": "aveimgcloud", "version": "1.0.0", "description": "Comprehensive image hosting web application with Vue.js frontend and PHP backend", "private": true, "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && php artisan serve --host=0.0.0.0 --port=8000", "build": "npm run install:deps && cd frontend && npm run build && cd ../backend && composer install --optimize-autoloader --no-dev", "install:deps": "cd frontend && npm ci && cd ../backend && composer install", "build:backend": "cd backend && composer install --optimize-autoloader --no-dev", "preview": "cd frontend && npm run preview", "install:all": "npm install && cd frontend && npm install && cd ../backend && composer install", "setup": "npm run install:all && npm run setup:env", "setup:env": "cp .env.example .env && cd frontend && cp .env.example .env && cd ../backend && cp .env.example .env", "backend:key": "cd backend && php artisan key:generate", "backend:migrate": "cd backend && php artisan migrate", "backend:seed": "cd backend && php artisan db:seed"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["image-hosting", "vue", "php", "laravel", "appwrite", "tailwind", "vercel"], "author": "RjNlxe", "license": "MIT"}